import Vue from 'vue'
import VueRouter from 'vue-router'


Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/login/start'
  },
  {
    path: '/login/start',
    name: 'start',
    component: resolve => (require(["@/components/login/start"], resolve))
  },
  {
    path: '/login/start1',
    name: 'start1',
    component: resolve => (require(["@/components/login/start1"], resolve))
  },
  {
    path: '/login/loginAfter',
    name: 'loginAfter',
    component: resolve => (require(["@/components/login/loginAfter"], resolve))
  },
  {
    path: '/login/login',
    name: 'login',
    component: resolve => (require(["@/components/login/login"], resolve))
  },
  {
    path: '/login/register',
    name: 'register',
    component: resolve => (require(["@/components/login/register"], resolve))
  },
  {
    path: '/login/reLogin',
    name: 'reLogin',
    component: resolve => (require(["@/components/login/reLogin"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'home',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'stockList',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/market/index',
    name: 'market',
    component: resolve => (require(["@/components/market/index"], resolve))
  },
  {
    path: '/trade/index',
    name: 'trade',
    component: resolve => (require(["@/components/trade/index"], resolve))
  },
  {
    path: '/favorite/index',
    name: 'favorite',
    component: resolve => (require(["@/components/favorite/index"], resolve))
  },
  {
    path: '/favorite/zxList',
    name: 'zxList',
    component: resolve => (require(["@/components/favorite/zxList"], resolve))
  },
  {
    path: '/favorite/moreIndexList',
    name: 'moreIndexList',
    component: resolve => (require(["@/components/favorite/moreIndexList"], resolve))
  },
  {
    path: '/favorite/moreList',
    name: 'moreList',
    component: resolve => (require(["@/components/favorite/moreList"], resolve))
  },
  {
    path: '/favorite/categoryList',
    name: 'categoryList',
    component: resolve => (require(["@/components/favorite/categoryList"], resolve))
  },
  {
    path: '/favorite/moreListCate',
    name: 'moreListCate',
    component: resolve => (require(["@/components/favorite/moreListCate"], resolve))
  },
  {
    path: '/favorite/editSelf',
    name: 'editSelf',
    component: resolve => (require(["@/components/favorite/editSelf"], resolve))
  },

  {
    path: '/favorite/search',
    name: 'search',
    component: resolve => (require(["@/components/favorite/search"], resolve))
  },
  {
    path: '/information/index',
    name: 'information',
    component: resolve => (require(["@/components/information/index"], resolve))
  },
  {
    path: '/information/payBag',
    name: 'payBag',
    component: resolve => (require(["@/components/information/payBag"], resolve))
  },
  {
    path: '/information/setting',
    name: 'setting',
    component: resolve => (require(["@/components/information/setting"], resolve))
  },
  {
    path: '/information/changeLang',
    name: 'changeLang',
    component: resolve => (require(["@/components/information/changeLang"], resolve))
  },

  {
    path: '/information/loginPass',
    name: 'loginPass',
    component: resolve => (require(["@/components/information/loginPass"], resolve))
  },
  {
    path: '/information/updatePassword',
    name: 'loginPass',
    component: resolve => (require(["@/components/information/updatePassword"], resolve))
  },
  {
    path: '/information/fundPass',
    name: 'fundPass',
    component: resolve => (require(["@/components/information/fundPass"], resolve))
  },
  {
    path: '/information/bankList',
    name: 'bankList',
    component: resolve => (require(["@/components/information/bankList"], resolve))
  },
  {
    path: '/information/addBankCard',
    name: 'addBankCard',
    component: resolve => (require(["@/components/information/addBankCard"], resolve))
  },
  {
    path: '/information/userPrivacy',
    name: 'userPrivacy',
    component: resolve => (require(["@/components/information/userPrivacy"], resolve))
  },

  {
    path: '/information/fundRecord',
    name: 'fundRecord',
    component: resolve => (require(["@/components/information/fundRecord"], resolve))
  },
  {
    path: '/information/exChange',
    name: 'exChange',
    component: resolve => (require(["@/components/information/exChange"], resolve))
  },
  {
    path: '/information/authInfo',
    name: 'authInfo',
    component: resolve => (require(["@/components/information/authInfo"], resolve))
  },
  {
    path: '/information/aboutUs',
    name: 'aboutUs',
    component: resolve => (require(["@/components/information/aboutUs"], resolve))
  },
  {
    path: '/information/tradeRule',
    name: 'tradeRule',
    component: resolve => (require(["@/components/information/tradeRule"], resolve))
  },
  {
    path: '/information/usdtAddress',
    name: 'usdtAddress',
    component: resolve => (require(["@/components/information/usdtAddress"], resolve))
  },
  {
    path: '/information/cashOut',
    name: 'cashOut',
    component: resolve => (require(["@/components/information/cashOut"], resolve))
  },
  {
    path: '/information/recharge',
    name: 'recharge',
    component: resolve => (require(["@/components/information/recharge"], resolve))
  },
  {
    path: '/information/rechargeChannel',
    name: 'rechargeChannel',
    component: resolve => (require(["@/components/information/rechargeChannel"], resolve))
  },
  {
    path: '/information/userInfo',
    name: 'userInfo',
    component: resolve => (require(["@/components/information/userInfo"], resolve))
  },
  {
    path: '/information/msgInfo',
    name: 'msgInfo',
    component: resolve => (require(["@/components/information/msgInfo"], resolve))
  },
  {
    path: '/information/loan',
    name: 'loan',
    component: resolve => (require(["@/components/information/loan"], resolve))
  },
  {
    path: '/information/loanRecords',
    name: 'loanRecords',
    component: resolve => (require(["@/components/information/loanRecords"], resolve))
  },

  {
    path: '/home/<USER>',
    name: 'newsList',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'newsDetail',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/trade/positionDetail',
    name: 'positionDetail',
    component: resolve => (require(["@/components/trade/positionDetail"], resolve))
  },
  {
    path: '/market/stockDetail',
    name: 'stockDetail',
    component: resolve => (require(["@/components/market/stockDetail"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'blockTrade',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'blockTrade1',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'blockTrade2',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'fllow',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'qcList',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'dayTrading',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'newStock',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'newStockZl',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'sgList',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'sgListJp',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'whList',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  
  {
    path: '/home/<USER>',
    name: 'newStockBuy',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'newStockBuyZl',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'newStockJp',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'etf',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/home/<USER>',
    name: 'coupons',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },

  {
    path: '/home/<USER>',
    name: 'newStockBuyJp',
    component: resolve => (require(["@/components/home/<USER>"], resolve))
  },
  {
    path: '/market/stockDetailzs',
    name: 'stockDetailzs',
    component: resolve => (require(["@/components/market/stockDetailzs"], resolve))
  },





























]

const router = new VueRouter({
  linkActiveClass: "navActive",
  mode: 'hash',
  scrollBehavior(to, from, savePosition) {
    if (savePosition) {
      return savePosition
    } else {
      return { x: 0, y: 0 }
    }
  },
  routes
})



export default router
