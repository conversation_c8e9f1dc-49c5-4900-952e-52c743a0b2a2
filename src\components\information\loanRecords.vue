<template>
	<div class="page">
		<!-- <top-back title="融資記錄"></top-back> -->
		<div class="list" v-if="daty.length">
			<div class="item" v-for="(item, index) in daty" :key="index">
				<div class="top">
					<!-- <div class="">{{ $t("時間") }}</div> -->
					<div class="t">{{ item.create_time }}</div>
				</div>
				<div class="flex flex-b bot">
					<div class="" style="padding-left: 0.1rem;">
						<div class="t1">{{ $formatMoney(item.money, 0) }}</div>
						<div class="t2">{{ $t("金額") }}</div>
					</div>
					<div class="">
						<!-- <div class="">{{ $t("狀態") }}</div> -->
						<div class="st flex flex-c"> <div class="point"></div> {{ $t(item.stat) }}</div>
					</div>
				</div>
				<div class="flex flex-b" style="padding: 0 0.1rem;">
					<div class="t2">{{ $t("訂單編號") }}</div>
					<div class="t2">
						{{String(item.order_number).substring( 0,String(item.order_number).length - 6)}}
					</div>
				</div>
				
			</div>
		</div>
		<no-data v-if="daty.length == 0"></no-data>
	</div>
</template>
<script>
	export default {
		name: "Record",
		data() {
			return {
				daty: [
					// {
					//   money: 1000,
					//   stat: "stat",
					//   order_number: "123456123456",
					//   create_time: new Date().getTime(),
					// },{
					//   money: 1000,
					//   stat: "stat",
					//   order_number: "123456123456",
					//   create_time: new Date().getTime(),
					// },{
					//   money: 1000,
					//   stat: "stat",
					//   order_number: "123456123456",
					//   create_time: new Date().getTime(),
					// },
				],
			};
		},
		components: {},
		mounted() {
			this.getdata();
		},
		methods: {
			getdata() {
				this.$server.post("/user/loanlist", {
					type: "twd"
				}).then((str) => {
					if (str.status == 1) {
						function timestampToTime(timestamp) {
							let date = new Date(parseInt(timestamp) * 1000);
							let y = date.getFullYear();
							let m = date.getMonth() + 1;
							m = m < 10 ? "0" + m : m;
							let d = date.getDate();
							d = d < 10 ? "0" + d : d;
							let h = date.getHours();
							h = h < 10 ? "0" + h : h;
							let minute = date.getMinutes();
							let second = date.getSeconds();
							minute = minute < 10 ? "0" + minute : minute;
							second = second < 10 ? "0" + second : second;
							// console.log(y + '-' + m + '-' + d + ' ' + '　' + h + ':' + minute + ':' + second);
							let dates =
								y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + second;
							return dates;
						}
						for (let i = 0; i < str.data.length; i++) {
							str.data[i].create_time = timestampToTime(str.data[i].create_time);
						}
						this.daty = str.data;
					}
				});
			},
		},
	};
</script>
<style lang="less" scoped="scoped">
	.page {
		padding: 0 0 0.1rem;
	}

	.list {
		padding: 0 0.1rem;
		.item {
			flex-wrap: wrap;
			padding: 0.1rem 0;
			line-height: 0.3rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			margin-bottom: 0.15rem;
			.top{
				padding-left: 0.1rem;
				border-bottom: 0.01rem solid #F3F3F3;
			}
			.bot{
				padding: 0.1rem 0 0;
			}
			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #9FA9BA;
			}
			.t1 {
				font-family: DINPro, DINPro;
				font-weight: 500;
				font-size: 0.21rem;
				color: #DA5B4C;
			}
			.t2{
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #9FA9BA;
			}
			.st {
				.point{
					width: 0.03rem;
					height: 0.03rem;
					background: #FFFFFF;
					border-radius: 50%;
					margin-right: 0.05rem;
				}
				padding: 0 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #FFFFFF;
				height: 0.2rem;
				background: #DCA25C;
				border-radius: 0.1rem 0rem 0rem 0.04rem;
			}
		}
	}
</style>