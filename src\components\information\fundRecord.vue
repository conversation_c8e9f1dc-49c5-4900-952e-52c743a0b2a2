<template>
	<div class="page">
		<top-back title="資金明細"></top-back>

		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<div class="nav-box flex flex-b">
					<div class="nav-item" v-for="(item, index) in navList" :key="index"
						:class="{ active: currmentIndex == item.type }" @click="changeNav(item.type)">
						{{ item.name }}
					</div>
				</div>
				<div class="list">
					<no-data v-if="!logList.length"></no-data>
					<div class="list-item" v-for="(item, index) in logList" :key="index">
						<template v-if="currmentIndex == 1">
							<div class="flex flex-b top">
								<div class="t">
									<!-- {{ formatText(0, item.detailed) }} -->
									{{ formatText1(item.detailed) }}
								</div>
                <div class="t" v-if="item.is_qc==2 && item.dz_type==1">主機當沖</div>
                <div class="t" v-if="item.is_qc==2 && item.dz_type==2">紅利</div>
							</div>
							<div class="flex flex-b bot">
								<div>
									<div class="t2">{{$formatDate("YYYY/MM/DD hh:mm:ss", item.create_time * 1000)}}</div>
									<div class="flex flex-b">
										<!-- <div class="time">交易訂單</div> -->
										<div class="t2 flex">
											{{ item.order_number }}
											<!-- <div class="icon copy" @click="copy(item.order_number)"></div> -->
										</div>
									</div>
								</div>
								<div class="t1" :class="parseFloat(item.money) > 0 ? 'red' : 'green'">{{ parseFloat(item.money) > 0 ? "+" : ""}}{{ $formatMoney(parseFloat(item.money),0) }}
								</div>
							</div>
						</template>
						<template v-else>
							<div class="flex flex-b top">
								<div class="t">{{ currmentIndex == 2 ? "儲值" : "提領" }}</div>
								<div class="status flex"> <div class="point"></div> {{ $t(item.stat) }}</div>
							</div>
							<div class="flex flex-b bot">
								<div>
									<div class="t2">{{$formatDate("YYYY/MM/DD hh:mm:ss", item.create_time * 1000) }}</div>
									<div class="flex flex-b">
										<!-- <div class="time">交易訂單</div> -->
										<div class="t2 flex">
											{{ item.order_number }}
											<!-- <div class="icon copy" @click="copy(item.order_number)"></div> -->
										</div>
									</div>
								</div>
								<div class="t1" :class="currmentIndex == 2 ? 'red' : 'green'">
									{{ currmentIndex == 2 ? "+" : "-" }}{{ $formatMoney(item.money,0) }}
								</div>
							</div>
						</template>
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "fundRecord",
		props: {},
		data() {
			return {
				isLoading: false,
				currmentIndex: 1,
				navList: [{
						name: "交易",
						type: 1
					},
					{
						name: "儲值",
						type: 2
					},
					{
						name: "提領",
						type: 3
					},
				],
				logList: [
					// {
					// 	detailed:546565,
					// 	money:100,
					// 	create_time:12333,
					// 	order_number:3232323,
					// 	stat:'已完成'
					// }
				],
			};
		},
		components: {},
		created() {},
		computed: {
			formatText() {
				return (index, value) => {
					let str = "";
					value = value.replace("！", " ");
					value = value.replace(",", "");
					if (index) {
						let start = value.indexOf("號");
						str = value.slice(start + 1);
					} else {
						let pIdx = value.indexOf(" ");
						let name = value.slice(0, pIdx);
						let mIdx = value.indexOf("碼");
						let gIdx = value.indexOf("名");
						let code = value.slice(mIdx + 1, gIdx - 2);
						str = `${this.$t(name)}`;
					}
					return str;
				};
			},
			formatText1() {
				return (value) => {
					value = value.replace(",", " ");
					value = value.replace("股票代码", "股票代碼/名稱");
					value = value.replace("股票名称", "");
					value = value.replace("充值", "儲值");
					value = value.replace("提现", "提領");

					return value;
				};
			},
		},
		mounted() {
			this.changeNav(1);
		},
		methods: {
			onRefresh() {
				this.changeNav(1);
			},
			changeNav(index) {
				this.currmentIndex = index;
				this.logList = [];
				this.getRecord();
			},
			getRecord() {
				this.$refs.loading.open(); //开启加载
				this.logList = [];
				if (this.currmentIndex == 1) {
					this.$server
						.post("/user/capitalloglist", {
							type: "twd",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}

				if (this.currmentIndex == 2) {
					this.$server
						.post("/user/rechargelist", {
							type: "twd",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}

				if (this.currmentIndex == 3) {
					this.$server
						.post("/user/withdrawallist", {
							type: "twd",
						})
						.then((res) => {
							this.$refs.loading.close();
							this.isLoading = false; //下拉刷新状态
							if (res.status == 1) {
								this.logList = res.data;
							}
						});
				}
			},

			copy(text) {
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select(); // 选中文本内容
				textarea.setSelectionRange(0, text.length); // 设置选定区的开始和结束点
				this.$toast("複製成功");
				var result = document.execCommand("copy"); //将当前选中区复制到剪贴板
				textarea.remove();
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.1rem;
		min-height: 100vh;
		background: #f7f7f7;

		.cot {
			.nav-box {
				margin: 0 0 0.1rem;
				background: #FFFFFF;
				.nav-item {
					padding: 0.1rem 0;
					flex: 1;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.15rem;
					color: #405476;
					text-align: center;
					position: relative;
					&::after {
						content: "";
						width: 100%;
						height: 0.02rem;
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						background: transparent;
					}

					&.active {
						color: #dba25c;
						font-weight: 500;
						&::after {
							background: #dba25c;
						}
					}
				}
			}
			.list {
				margin: 0 0.1rem;
				.list-item {
					padding: 0.1rem 0;
					background: #FFFFFF;
					box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
					border-radius: 0.07rem;
					margin-bottom: 0.15rem;
					.top{
            padding: 0 .1rem .1rem .1rem;
						border-bottom: 0.01rem solid #F3F3F3;
						.t {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.15rem;
							color: #333333;
						}
						.status{
							height: 0.2rem;
							background: #DCA25C;
							border-radius: 0.1rem 0rem 0rem 0.04rem;
							line-height: 0.2rem;
							padding: 0 0.1rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.12rem;
							color: #FFFFFF;
							.point{
								margin-right: 0.05rem;
								width: 0.03rem;
								height: 0.03rem;
								background: #FFFFFF;
								border-radius: 50%;
							}
						}
					}
					.bot{
						padding: 0 0.1rem;
						.copy {
							width: 0.1rem;
							height: 0.1rem;
							margin-left: 0.05rem;
						}
						.t1 {
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 0.2rem;
						
							&.green {
								color: #6bb831;
							}
						
							&.red {
								color: #cf2829;
							}
						}
						.t2 {
							padding-top: 0.1rem;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 0.13rem;
							color: #9FA9BA;
						}
					}
				}
			}
		}
	}
</style>