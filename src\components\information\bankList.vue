<template>
	<div class="page ">
		<top-back title="銀行帳戶管理" :isList="true"></top-back>
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="cot">
				<!-- <div class="flex flex-b top">
					<div class="t">我的卡 (共{{ bankList.length }}張)</div>
					<div class="flex bt" @click="$toPage('/information/addBankCard')">
						<div class="icon add animate__animated animate__fadeIn"></div>
						<div class="t1">添加金融卡</div>
					</div>
				</div> -->
				<div class="list">
					<no-data v-if="!bankList.length"></no-data>
					<div class="item" v-for="(item, index) in bankList" :key="index">
						<div class="flex flex-b mb">
							<div class="">
								<div class="name">{{ item.bank_name }}</div>
								<!-- <div class="tt">{{ $t("储存卡") }}</div> -->
							</div>
							<div class="icon del animate__animated animate__fadeIn" @click="delbakcard(item.id)"></div>
						</div>
						<div>
							<!-- <div class="flex mb">
								<div class="t1">戶名：</div>
								<div class="t">{{ item.realname }}</div>
							</div> -->
							<div class="flex">
								<!-- <div class="t1">帳號：</div> -->
								
								<div class="flex">
									<div class="t" v-if="!item.showFlag">{{ formatNum(item.bank_num) }}</div>
									<div class="t" v-else>{{ item.bank_num }}</div>
									<img src="../../assets/zhengy.png" @click="showClick(item,item.showFlag)" v-if="item.showFlag" style="width: .2rem;height: .2rem;margin-left: .1rem;">
									<img src="../../assets/biy.png" @click="showClick(item,item.showFlag)" v-else style="width: .2rem;height: .2rem;margin-left: .1rem;">
								</div>
							</div>
						</div>
					</div>
					<div class="addBox flex flex-c" @click="$toPage('/information/addBankCard')"><span style="font-size: 0.2rem;margin-right: 0.02rem;">+</span>添加</div>
				</div>
			</div>
		</van-pull-refresh>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "bankList",
		props: {},
		data() {
			return {
				isLoading: false,
				bankList: [
					// {
					//   realname: "realname",
					//   bank_name: "bank_name",
					//   bank_num: "123456",
					// },
				],
				flag: false,
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {
			formatNum() {
				return (value) => {
					let str = value.slice(0, 4);
					return `${str} **** **** ****`;
				};
			},
		},
		mounted() {
			// this.$refs.firstLoading.open();
		},
		methods: {
			showClick(e,flag){
				this.$set(e,'showFlag',!flag)
			},
			onRefresh() {
				this.initData();
			},
			delbakcard(bankid) {
				if (this.flag) return;
				this.$refs.loading.open(); //开启加载
				this.flag = true;
				this.$server
					.post("/user/delbakcard", {
						bankid,
						type: "twd"
					})
					.then((res) => {
						this.$refs.loading.close();

						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							setTimeout(() => {
								this.initData();
							}, 1500);
						}
					});
			},
			initData() {
				this.$server
					.post(
						"/user/cardList", {
							size: 100,
							page: 1,
							type: "twd"
						},
						(failres) => {
							that.bankList = [];
						}
					)
					.then((res) => {
						// this.$refs.firstLoading.close();
						this.isLoading = false;
						this.flag = false;
						var arr = [];
						for (var i in res.data) {
							var row = res.data[i];
							if (row.bank_name != "TRC" && row.bank_name != "ERC") {
								arr.push(row);
							}
						}
						this.bankList = arr;
						this.bankList.forEach(item=>{
							this.$set(item,'showFlag',false)
						})
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0 0.2rem;
		min-height: 100vh;
		.padd {
			padding: 0.1rem 0;
			.b-btn {
				margin: 0;
				font-size: 0.12rem;
				color: #888888;
				background-color: transparent;

				.icon {
					margin-right: 0.1rem;
				}
			}
		}

		.cot {
			padding-top: 0.1rem;
			.top {
				padding: 0.2rem 0.15rem 0.4rem;
				background: linear-gradient(90deg, #59c08f 0%, #77c4da 100%);

				.t {
					font-weight: 600;
					font-size: 0.16rem;
					color: #ffffff;
				}

				.bt {
					background: #ffffff;
					border-radius: 0.3rem;
					padding: 0.05rem 0.1rem;

					.t1 {
						font-size: 0.12rem;
						color: #549d7e;
						margin-left: 0.05rem;
					}
				}
			}

			.list {
				background: #f7f7f7;
				margin: -0.2rem 0 0;
				min-height: 100vh;
				border-radius: 0.2rem 0.2rem 0 0;
				padding: 0.2rem 0.15rem;
				.addBox{
					height: 0.44rem;
					padding: 0 0.2rem;
					background: #FFFFFF;
					box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
					border-radius: 0.25rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.14rem;
					color: #182133;
				}
				.item {
					height: 1.06rem;
					background: linear-gradient( 90deg, #3E7BC2 0%, #1C6AC4 100%);
					box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
					border-radius: 0.07rem 0.07rem 0rem 0rem;
					margin-bottom: 0.15rem;
					padding: 0.15rem;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					&:nth-child(2n){
						background: linear-gradient( 135deg, #E98F5A 0%, #DE634E 100%);
					}
					.t {
						
						font-family: AppleSystemUIFont;
						font-size: 0.2rem;
						color: #FFFFFF;
						font-weight: 600;
					}

					.t1 {
						color: #929292;
					}
					.mb {
						margin-bottom: 0.15rem;
					}

					.name {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 0.13rem;
						color: #FFFFFF;
					}
					.tt {
						font-size: 0.12rem;
						color: #7f7f7f;
						margin-top: 0.05rem;
					}
				}
			}
		}
	}
</style>