<template>
	<!-- 个人 -->
	<div class="page">
		<van-pull-refresh v-model="isLoading" @refresh="onRefresh" :loosing-text="$t('new').t3"
			:loading-text="$t('new').t4" :pulling-text="$t('new').t5">
			<div class="pg">
				<div class="top">
					<div class="flex flex-b user-info">
						<div class="flex">
							<div class="icon user animate__animated animate__fadeIn" @click="$toPage('/information/setting')"></div>
							<div>
								<div class="name flex">
									尊貴的：
                  <template v-if="flagAccount">{{ userInfo.realname }}</template>
                  <template v-else>******</template>
									<div class="icon animate__animated animate__fadeIn" :class="flagAccount ? 'by12' : 'zy12'" @click="flagAccount=!flagAccount"> </div>
								</div>
<!--								<div class="account" v-if="userInfo.id_card">-->
<!--									身分證字號：{{flagAccount?userInfo.id_card :visibilityChange(userInfo.id_card) }}-->
<!--								</div>-->
								<div class="account">
									數位證券帳號：
                  <template v-if="flagAccount">{{userCard }}</template>
                  <template v-else>******</template>
								</div>

							</div>
						</div>
						<div class="icon xx" @click="$toPage('/information/userInfo')"></div>
						<!-- <div class="rz-btn" @click="$toPage('/information/authInfo')">
							{{ userInfo.is_true == 1 ? "已實名" : "未實名" }}
						</div> -->
					</div>
					<div class="flex flex-c">
						<div class="criclie flex-column-item" @click="show=!show">
							<div class="flex">
								<div class="tit">我的總資產</div>
								<div class="icon zy12" :class="{by12:show}"></div>
							</div>
							<div class="price">{{ show ? $formatMoney(totalAssets,0) || 0 : "****" }}</div>
						</div>
					</div>
					<div class="money">
						<div class="flex flex-b tops" v-if="false">
							<div class="flex-1">
								<div class="t flex" @click="show = !show">
									總資產
									<div class="icon animate__animated animate__fadeIn" :class="show ? 'by12' : 'zy12'">
									</div>
								</div>
								<div class="num">
									{{ show ? $formatMoney(totalAssets,0) || 0 : "****" }}
								</div>
							</div>
							<!-- 图 -->
							<!-- <div class="animate__animated animate__fadeIn">
								<div class="" id="main"></div>
							</div> -->
						</div>
						<div class="nums">
							<div class="item">
								<div class="t2">可用資金</div>

								<div class="t1">
									{{ show ? $formatMoney(userInfo.twd,0) || 0 : "****" }}
								</div>
							</div>
							<div class="item flex-1">
								<div class="t2">盈虧資金</div>

								<div class="t1">
									{{ show ? $formatMoney(totalProfit,0) || 0 : "****" }}
								</div>
							</div>
							<!-- <div class="item">
								<div class="t1">
									{{ show ? $formatMoney(freezeAssets,0) || 0 : "****" }}
								</div>
								<div class="t2">凍結資金</div>
							</div> -->
							<div class="item">
								<div class="t2">總資金</div>

								<div class="t1">
									{{ show ? $formatMoney(totalAssets,0) || 0 : "****" }}
								</div>
							</div>
							<!-- <div class="item">
								<div class="t1">{{ $formatMoney(userInfo.dollar,0) }}</div>
								<div class="t2">{{ $t("new").a36 }} (USD)</div>
							</div> -->
						</div>
					</div>
				</div>
				<div class="tabs">
					<div class="tab-item flex flex-b" v-for="(item, i) in tabList" :key="i" @click="goUrl(item.url)">
						<div class="flex ">
							<!-- <div class="icon animate__animated animate__fadeIn" :class="item.icon"></div> -->
							<div class="t">{{ item.name }}</div>
						</div>
						<div class="icon more animate__animated animate__fadeIn"></div>
					</div>
				</div>
			</div>
		</van-pull-refresh>
		<tab-bar :current="4"></tab-bar>
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "information",
		props: {},
		data() {
			return {
				flagAccount:true,
				show: true,
				loading: true,
				isLoading: false,
				userInfo: {},
				tabList: [
					{
						name:'儲值',
						// url:'/information/recharge',
						url:'kefu'
					},
					{
						name:'提領',
						url:'/information/cashOut'
					},
					{
						name: "資金明細",
						icon: "zjmx",
						url: "/information/fundRecord",
					},
					// {
					// 	name: "實名認證",
					// 	icon: "m1",
					// 	url: "/information/authInfo",
					// },
					{
						name: "銀行帳戶管理",
						icon: "m4",
						url: "/information/bankList",
					},
					{
						name:'融資中心',
						url:'/information/loan'
					},
					
					{
						name: "修改密碼",
						icon: "m2",
						url: "/information/updatePassword",
					},
					// {
					// 	name: "資金密碼",
					// 	icon: "m3",
					// 	url: "/information/fundPass",
					// },
					{
						name: "交易規則",
						icon: "m5",
						url: "/information/tradeRule",
					},
					// {
					// 	name: "關於我們",
					// 	icon: "m6",
					// 	url: "/information/aboutUs",
					// },
					
					// {
					//   name: this.$t("mine").menu9,
					//   icon: "m6",
					//   url: "/information/changeLang",
					// },
					// {
					// 	name: "營業員",
					// 	icon: "m7",
					// 	url: "kefu",
					// },
				],
        userCard: '',
		totalAssets:0,
		totalProfit:0,
		totalProfit:0
			};
		},
		computed: {},
		mounted() {
			this.getTotalProfit();
			this.getTotalAssets();
		},
		methods: {
			visibilityChange(val,wei=4) {
				if(!val){
					return;
				}
				let _len = val.length;
				return val.substring(0, 4) + '****' + val.substring(_len - wei, _len)
			},
			//盈利資金
			async getTotalProfit() {
				const res = await this.$server.post("/trade/userstocklists", {
					type: "twd",
				});
				this.isLoading = false;
				let fdyk = 0;
				let arr = [];
				if (res.status == 1) {
					res.data.forEach((item) => {
						arr.push(Number(item.yingkui));
					});

					fdyk = arr.reduce((a, b) => a + b, 0);
				}
				this.totalProfit = fdyk;
			},
			// 获取总资产
			async getTotalAssets() {
				// this.$refs.loading.open(); //开启加载

				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
          let mm = this.userInfo.twd.toString().split('.')
          this.userInfo.twd = mm[0]
          this.userCard = this.userInfo.agent_id + '' +this.userInfo.id
          let a
          for (a=0;a<6-this.userCard.length;a++){
            this.userCard = '0' + this.userCard
          }
				}
				let abalance = Number(res.data.twd || 0); //用户可用余额

				// 获取跟单盈亏
				// const res1 = await this.$server.post("/trade/userproductlist", {
				//   type: "twd",
				// });
				// let followingFreeze = 0; //量化跟单的认缴冻结
				// if (res1.status == 1 && res1.data && Array.isArray(res1.data)) {
				//   let arr = [];
				//   res1.data.forEach((item) => {
				//     if (item.status == 0) {
				//       arr.push(Number(item.money)); //跟单冻结的资金
				//     }
				//   });
				//   let total = arr.reduce((a, b) => a + b, 0);
				//   followingFreeze = total;
				// }

				// 申購列表的投資
				const res2 = await this.$server.post("/trade/usernewstocklist", {
					type: "twd",
					buy_type: 0,
				});
				let subscriptionProfit = 0; //新股申请的认缴冻结
				if (res2.status == 1 && res2.data && Array.isArray(res2.data)) {
					let arr = [];
					let arr1 = [];
					res2.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit = total + total1;
				}

				// 競拍列表的投資
				const res3 = await this.$server.post("/trade/usernewstocklist", {
					type: "twd",
					buy_type: 1,
				});
				let subscriptionProfit1 = 0; //新股申请的认缴冻结
				if (res3.status == 1 && res3.data && Array.isArray(res3.data)) {
					let arr = [];
					let arr1 = [];
					res3.data.forEach((item) => {
						if (item.status == 1) {
							arr.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //認繳的资金
						}
						if (item.status == 0) {
							arr1.push(Number(item.rjmoney == "-" ? 0 : item.rjmoney)); //计算申购认缴等于0的
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					let total1 = arr1.reduce((a, b) => a + b, 0);

					subscriptionProfit1 = total + total1;
				}

				// 持仓中的申请冻结
				const res4 = await this.$server.post("/trade/userstocklist", {
					type: "twd",
				});
				let positionFreeze = 0;
				delete res4.data.ccsz;
				delete res4.data.fdyk;
				let dataArr = Object.values(res4.data);
				if (dataArr.length) {
					let arr = [];
					dataArr.forEach((item) => {
						if (item.status == 0) {
							// arr.push(Number(item.credit)); //認繳的资金
							arr.push(
								Number(item.buy_price) * Number(item.stock_num) + item.yingkui
							); //認繳的资金  买入本金+盈利
						}
					});

					let total = arr.reduce((a, b) => a + b, 0);
					positionFreeze = total;
				}

				// 大宗交易申请冻结
				const res5 = await this.$server.post("/trade/ustockslist", {
					type: "twd",
				});
				let bigDealFreeze = 0;
				if (res5.status == 1 && res5.data && Array.isArray(res5.data)) {
					let arr = [];
					res5.data.forEach((item) => {
						if (item.status == 0) {
							arr.push(Number(item.credit)); //認繳的资金
						}
					});
					let total = arr.reduce((a, b) => a + b, 0);
					bigDealFreeze = total;
				}

				// 冻结资产
				this.freezeAssets =
					subscriptionProfit +
					bigDealFreeze +
					positionFreeze +
					subscriptionProfit1;
				//  + followingFreeze
				// 总资产
				this.totalAssets = abalance + this.freezeAssets;
				this.$refs.loading.close();
			},
			exit() {
				localStorage.clear();
				this.$toPage("/login/login");
			},
			goUrl(url) {
				if (url == "kefu") {
					this.getConfig();
				} else {
					this.$toPage(url);
				}
			},
			// 下拉刷新
			onRefresh() {
				this.getTotalProfit();
				this.getTotalAssets();
			},
			async getConfig() {
				this.$refs.loading.open();
				const res = await this.$server.post("/common/config", {
					type: "all"
				});
				let val = {};
				res.data.forEach((vo) => {
					val[vo.name] = vo.value;
				});
				this.$refs.loading.close();
				this.$openUrl(val.kefu); //重新获取
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		min-height: 100vh;
		padding: 0 0 1rem;
	}

	.pg {
		.top {
			padding: 0.15rem;
			height: 3.6rem;
			background: url('../../assets/<EMAIL>?t=1') no-repeat;
			background-size: 100% 100%;
		}
		.user-info {
			margin: 0 0 0.15rem;
		
			.name {
				margin-left: 0.1rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}
		
			.account {
				margin-left: 0.1rem;
				margin-top: 0.05rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #B0C0E4;
			}
		
			.xx {
				margin-left: 0.16rem;
			}
			.rz-btn {
				background: #549d7e;
				border-radius: 0.04rem;
				color: #ffffff;
				padding: 0.05rem 0.1rem;
			}
		}
		.criclie {
			position: relative;
			z-index: 9;
			margin-top: 0.3rem;
			width: 2rem;
			height: 2rem;
			background: rgba(24, 33, 51, 0.3);
			box-shadow: 0rem 0rem 0.1rem 0rem rgba(220, 162, 92, 0.5);
			border: 0.03rem solid #DCA25C;
			border-radius: 50%;
			padding: 0.4rem 0;
			display: flex
;
    align-items: center;
    justify-content: center;
		
			.tit {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #fff;
				margin-right: 0.05rem;
			}
		
			.price {
				padding: 0.14rem 0;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.24rem;
				color: #fff;
			}
		}
		.money {
			position: relative;
			z-index: 99;
			margin: 0.4rem 0 0;
			padding: 0.15rem 0.1rem;
			background: #ffffff;
			box-shadow: 0rem 0.04rem 0.04rem 0rem rgba(0, 0, 0, 0.12),
				0rem -0.02rem 0.04rem 0rem rgba(123, 187, 161, 0.25);
			border-radius: 0.12rem 0.12rem 0.12rem 0.12rem;
		
			.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 0.1rem;
		
				.t1 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.15rem;
					color: #182133;
					margin-bottom: 0.05rem;
				}
		
				.t2 {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #5D6C8D;
				}
			}
		}
	}

	.tabs {
		margin: 1rem 0.15rem 0;
		.tab-item {
			margin: 0.1rem 0;
			height: 0.5rem;
			padding: 0 0.1rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 0.13rem;
				color: #182133;
			}
		}
	}

	

	
</style>