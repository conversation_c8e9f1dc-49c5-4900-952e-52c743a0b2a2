<template>
	<div class="page ">
		<div class="bg">
			<div class="flex flex-b otg">
				<div class="flex">
					<div class="icon rjt animate__animated animate__fadeIn" @click="goBack"></div>
					<div class="flex-1 t">個人資料</div>
				</div>
				<div class="icon xx" @click="$toPage('/information/userInfo')"></div>
			</div>
			<div class="flex">
				<div class="icon user animate__animated animate__fadeIn"></div>
				<div>
					<div class="name">{{ userInfo.realname }}</div>
					<!-- <div class="account">{{ userInfo.account }}</div> -->
					<div class="rz-btn" @click="$toPage('/information/authInfo')">
						{{ userInfo.is_true == 1 ? "已實名" : "未實名" }}
					</div>
				</div>
			</div>
		</div>
		<div class="bg02">
			<div class="tab01 flex flex-b">
				<div class="tabItem flex-column-item" v-for="(item,index) in nav01" :key="index" @click="goUrl(item.url)">
					<div class="icon" :class="item.icon"></div>
					<div class="name">{{item.name}}</div>
				</div>
			</div>
			<div class="tab02">
				<div class="tabItem flex flex-b" v-for="(item,index) in nav02" :key="index" @click="goUrl(item.url)">
					<div class="name">{{item.name}}</div>
					<div class="icon more"></div>
				</div>
			</div>
		</div>
		<!-- <div class="list">
			<div class="item flex flex-b">
				<div class="t">頭像</div>
				<div class="icon user animate__animated animate__fadeIn"></div>
			</div>
			<div class="item flex flex-b">
				<div class="t">姓名</div>
				<div class="t1">{{ userInfo.realname }}</div>
			</div>
			<div class="item flex flex-b">
				<div class="t">手機號</div>
				<div class="t1">{{ userInfo.account }}</div>
			</div>
		</div> -->
		<div class="exit" @click="exit">登出</div>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "setting",
		props: {},
		data() {
			return {
				userInfo: {},
				nav01:[
					{
						icon:'m1',
						name:'實名認證',
						url: "/information/authInfo",
					},
					{
						icon:'wdzc',
						name:'個人中心',
						url: "/information/index",
					},
					{
						icon:'wdcc',
						name:'我的持倉',
						url: "/trade/index",
					},
					{
						icon: 'm7',
						name:'VIP管家',
						url:'kefu'
					}
				],
				nav02:[
					{
						name: "登入密碼",
						icon: "m2",
						url: "/information/loginPass",
					},
					{
						name: "資金密碼",
						icon: "m3",
						url: "/information/fundPass",
					},
					{
						name: "交易規則",
						icon: "m5",
						url: "/information/tradeRule",
					},
					// {
					// 	name: "關於我們",
					// 	icon: "m6",
					// 	url: "/information/aboutUs",
					// },
				]
			};
		},
		components: {},
		created() {
			this.initData();
		},
		computed: {},
		methods: {
			goUrl(url) {
				if (url == "kefu") {
					this.getConfig();
				} else {
					this.$toPage(url);
				}
			},
      async getConfig() {
        this.$refs.loading.open();
        const res = await this.$server.post("/common/config", {
          type: "all"
        });
        let val = {};
        res.data.forEach((vo) => {
          val[vo.name] = vo.value;
        });
        this.$refs.loading.close();
        this.$openUrl(val.kefu); //重新获取
      },
			goBack() {
        this.$router.go(-1);
			},
			exit() {
				let phone = this.$storage.get("AC")
				let password = this.$storage.get("PS")
				localStorage.clear();
				this.$storage.save("AC", phone);
				this.$storage.save("PS", password);
				this.$toPage("/login/login");
			},
			initData() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.exit {
		height: 0.42rem;
		background: #253047;
		box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
		border-radius: 0.07rem;
		line-height: 0.42rem;
		text-align: center;
		margin: 0.3rem 0.1rem;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 0.13rem;
		color: #FFFFFF;
	}
	::v-deep .header{
		background-color: transparent;
	}
	.page {
		padding: 0 0rem 0.2rem;
		min-height: 100vh;
		.bg{
			height: 3.6rem;
			background: url('../../assets/v2/bg03.png') no-repeat center/100%;
			padding: 0.1rem 0.15rem;
			.otg {
				height: 0.5rem;
				margin-bottom: 0.1rem;
				.t {
					padding-left: 0.1rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #FFFFFF;
				}
			}
			.user{
				margin-right: 0.1rem;
			}
			.name{
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;
			}
			.code{
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #DCA25C;
			}
			.rz-btn{
				margin-top: 0.05rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #DCA25C;
			}
		}
		.bg02{
			margin-top: -2.2rem;
			background: #F6F8FE;
			border-radius: 0.15rem 0.15rem 0rem 0rem;
			padding: 0.15rem 0.1rem;
			.tab01{
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.02rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				padding: 0.1rem;
				.name{
					margin-top: 0.05rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 0.12rem;
					color: #000000;
				}
			}
			.tab02{
				margin-top: 0.15rem;
				background: #FFFFFF;
				box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
				border-radius: 0.07rem;
				padding:0.05rem 0.15rem;
				.tabItem{
					padding: 0.1rem 0;
				}
			}
		}
		.list {
			padding: 0 0.15rem;

			.item {
				padding: 0.15rem 0;
				border-bottom: 0.01rem solid #dedede;

				.t {
					color: #0e1028;
				}

				.t1 {
					// font-weight: 600;
					color: #0e1028;
				}
			}
		}
	}
</style>