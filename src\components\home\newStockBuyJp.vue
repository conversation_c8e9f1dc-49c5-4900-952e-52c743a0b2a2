<template>
	<div class="page ">
		<top-back title="競拍詳情"></top-back>

		<div class="cot">
			<div class="item">
				<div class="item-top flex flex-b">
					<div class="">
						<div class="name">{{ item.name || "-" }}</div>
						<div class="code">{{ item.symbol || "-" }}</div>
					</div>
					<div class="item-list bt">
						溢價差
						{{ item.price - item.bprice > 0 ? "+" : ""}}{{((item.price - item.bprice) / item.bprice) * 100 == -100? "-": (((item.price - item.bprice) / item.bprice) * 100).toFixed(2)}}%
					</div>
				</div>
				<div class="item-middle flex flex-b">
					<div class="item-list flex flex-b">
						<div class="t2">市價</div>
						<div class="t3" :class="item.price - item.bprice < 0 ? 'green' : 'red'">
							{{ $formatMoney(item.price) }}
						</div>
					</div>

					<div class="item-list flex flex-b">
						<div class="t2 ">總申購</div>
						<div class="t3">
							{{ $formatMoney(item.num, 0) }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">差價</div>
						<div class="t3">
							{{ $formatMoney(item.price - item.bprice) }}
						</div>
					</div>
					<div class="item-list flex flex-b">
						<div class="t2 ">承銷價</div>
						<div class="t3 ">
							{{ $formatMoney(item.bprice) }}
						</div>
					</div>

					<!-- <div class="item-list flex flex-b">
						<div class="t2 ">申購期間</div>
						<div class="t3 ">
							{{ item.subdate }}
						</div>
					</div>

					<div class="item-list flex flex-b">
						<div class="t2 ">截止日</div>
						<div class="t3 ">
							{{ item.endTime }}
						</div>
					</div>

					<div class="item-list flex flex-b">
						<div class="t3">撥券日</div>
						<div class="t2">{{ item.amtdate }}</div>
					</div> -->
				</div>
			</div>

			<div class="info">
				<div class="title">基本信息</div>

				<div class="info-item flex flex-b">
					<div class="t">開始日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", item.start * 1000) }}
					</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">截止日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", item.end * 1000) }}
					</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">中籤日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.gb_date)) }}</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">撥券日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.fq_date)) }}</span>
				</div>

				<div class="info-item flex flex-b">
					<div class="t">上市日</div>
					<span class="t1">
						{{ $formatDate("YYYY/MM/DD", new Date(item.ss_date)) }}</span>
				</div>
			</div>

			<div class="info">
				<div class="title">發行信息</div>

				<div class="">
					<div class="info-item flex flex-b">
						<div class="t">發行市場</div>
						<span class="t1">
							<!-- {{ item.market }} -->
							-
						</span>
					</div>

					<!-- <div class="info-item flex flex-b">
            <div class="t">單投標處理費</div>
            <div class="t1  num-font">
              {{
                $formatMoney(
                  parseFloat((item.bprice * item.charge) / 100).toFixed(2)
                )
              }}
            </div>
          </div> -->

					<div class="info-item flex flex-b">
						<div class="t">實際承銷價</div>
						<span class="t1">{{ item.price }}</span>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">主辦券商</div>
						<div class="t1">{{ item.name || "-" }}</div>
					</div>
					<div class="info-item flex flex-b">
						<div class="t">暫時承銷價</div>
						<span class="t1">{{ $formatMoney(item.bprice) || 0 }}</span>
					</div>
				</div>
			</div>

			<!-- 是否可申购 -->
			<div class="bottom" v-if="item.isKsg">
				<div class="wbg">
					<div class="flex flex-b">
						<div class="t">競拍數量</div>
						<div class="t">
							帳戶可用資金：{{ $formatMoney(userInfo.abalance) }}
						</div>
					</div>

					<input v-model="quantity" @input="quantity = quantity.replace(/[^0-9]/g, '')" placeholder="請輸入競拍數量"
						type="number" />

					<div class="jg">
						<!-- 競價價格-->
						<div class="flex flex-b">
							<div class="t">競拍價格</div>
						</div>

						<input v-model="price" @input="price = price.replace(/[^0-9]/g, '')" placeholder="請輸入競拍價格"
							type="number" />
					</div>
				</div>
			</div>
			<div @click="submitSg" class="b-btn animate__animated animate__fadeIn">
				競拍
			</div>
		</div>

		<!--点击按钮加载效果组件 -->
		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "newStockBuyJp",
		data() {
			return {
				item: this.$storage.get("itemTemp") || {},
				quantity: "",
				price: "",
				flag: false,
				type: 0,
				userInfo: {},
			};
		},
		created() {
			this.type = this.$route.query.type;
			this.getUserInfo();
		},
		methods: {
			async getUserInfo() {
				const res = await this.$server.post("/user/getUserinfo", {
					type: "twd"
				});
				if (res.status == 1) {
					this.userInfo = res.data;
				}
			},
			submitSg() {
				if (!this.quantity) {
					this.$toast("請輸入競拍數量");
					return;
				}

				if (this.quantity < 1) {
					this.$toast("競拍數量不得低於1張");
					return;
				}

				if (this.flag) {
					return;
				}

				this.flag = true;
				this.$refs.loading.open(); //开启加载

				this.$server
					.post("/trade/buy_newstock", {
						symbol: this.item.symbol,
						zhang: this.quantity,
						type: "twd",
						id: this.item.id,
						price: this.price,
						buy_type :1
					})
					.then((res) => {
						this.$refs.loading.close(); //关闭加载

						this.$toast(this.$formText(res.msg).replace('单只新股，只能申购',this.$t('单只新股，只能申购')));
						this.getUserInfo();
						setTimeout(() => {
							this.flag = false;
						}, 2000);
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.6rem 0.1rem 0.1rem;
		min-height: 100vh;
	}

	.cot {
		.item {
			background: #ffffff;
			box-shadow: 0rem 0.02rem 0.02rem 0rem rgba(125, 187, 179, 0.12),
				0rem -0.02rem 0.02rem 0rem rgba(126, 187, 180, 0.12);
			border-radius: 0.06rem 0.06rem 0.06rem 0.06rem;
			padding: 0.1rem;
			margin-bottom: 0.15rem;

			.red {
				color: #cf2829;
			}

			.t3 {
				font-size: 0.12rem;
			}

			.item-top {
				.name {
					font-size: 0.14rem;
					color: #000000;
					// font-weight: bold;
				}

				.code {
					font-size: 0.12rem;
					color: #909090;
					margin-top: 0.05rem;
				}

				.t3 {
					font-size: 0.16rem;
				}

				.t4 {
					font-size: 0.12rem;
					color: #909090;
					margin-top: 0.05rem;
				}

				.bt {
					background: #dba25c;
					border-radius: 0.04rem;
					font-size: 0.12rem;
					color: #fff;
					padding: 0.05rem 0;
					min-width: 1.2rem;
					text-align: center;
				}
			}

			.item-middle {
				flex-wrap: wrap;
				margin-top: 0.1rem;

				.item-list {
					// line-height: 0.3rem;
					width: 46%;
					margin-bottom: 0.1rem;

					.t2 {
						font-size: 0.12rem;
						color: #9f9fa3;
						margin-top: 0.05rem;
					}

					.t3 {
						font-size: 0.14rem;
						color: #0d0d0d;
					}

					.green {
						color: #68a03d;
					}

					.red {
						color: #ba3b3a;
					}
				}
			}
		}

		.info {
			margin-bottom: 0.1rem;
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0, 0, 0, 0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 0.15rem;
				color: #333333;
			}

			.info-item {
				padding: 0.15rem 0;
				border-bottom: 0.01rem solid #ededed;

				&:last-child {
					border-bottom: 0;
				}

				.t {
					font-size: 0.12rem;
					color: #5c5c5c;
				}

				.t1 {
					font-size: 0.12rem;
					color: #272a2f;
				}
			}
		}

		.bottom {
			background: #FFFFFF;
			box-shadow: 0rem 0.01rem 0.05rem 0rem rgba(0,0,0,0.16);
			border-radius: 0.07rem;
			padding: 0.1rem;
			.wbg {
				padding: 0.1rem 0;
		
				.t {
					font-size: 0.12rem;
				}
		
				input {
					width: 100%;
					height: 0.4rem;
					margin: 0.1rem 0 0.2rem;
					background: transparent;
					border-bottom: 0.01rem solid #cecece;
					color: #000;
					&::placeholder {
						font-size: 0.15rem;
						color: #9a9fa5;
					}
				}
			}
		
			.b-btn {
				margin: 0.1rem 0rem;
			}
		}

		.top {
			padding: 0.1rem;

			.txt {
				font-size: 0.12rem;
				color: #464646;
				margin-top: 0.05rem;
			}

			.st-btn {
				font-weight: 500;
				font-size: 0.12rem;
				color: #c5585e;
			}
		}
	}
</style>