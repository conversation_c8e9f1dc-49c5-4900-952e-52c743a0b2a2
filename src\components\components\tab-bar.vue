<template>
	<div class="tab">
		<div class="flex flex-b">
			<div class="item" :class="{ active: current == 0 }" @click="$replacePage('/home/<USER>')">
				<div class="icon a animate__animated animate__fadeIn" :class="{ ac: current == 0 }"></div>
				<div class="t">探索</div>
			</div>
			<!-- 自选 -->
			<div class="item" :class="{ active: current == 3 }" @click="$replacePage('/favorite/zxList')">
				<div class="icon a1 animate__animated animate__fadeIn" :class="{ ac1: current == 3 }"></div>
				<div class="t">
					<!-- 行情 -->
					自選
				</div>
			</div>
			<!-- 數位當沖 -->
			<div class="item" :class="{ active: current == 1 }" @click="$replacePage('/home/<USER>')">
				<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 1 }"></div>
				<div class="t">數位當沖</div>
			</div>

			<!-- 持仓 -->
			<div class="item" :class="{ active: current == 2 }" @click="$replacePage('/trade/index')">
				<div class="icon a2 animate__animated animate__fadeIn" :class="{ ac2: current == 2 }"></div>
				<div class="t">交易</div>
			</div>
			
			<div class="item" :class="{ active: current == 4 }" @click="$replacePage('/information/index')">
				<div class="icon a3 animate__animated animate__fadeIn" :class="{ ac3: current == 4 }"></div>
				<div class="t">
					<!-- 行情 -->
					個人中心
				</div>
			</div>
			<!-- 钱包 -->
			<!-- <div class="item" :class="{ active: current == 4 }" @click="$replacePage('/information/payBag')">
				<div class="icon a4 animate__animated animate__fadeIn" :class="{ ac4: current == 4 }"></div>
				<div class="t">{{ $t("钱包") }}</div>
			</div> -->
			
			<!-- <div class="item" :class="{ active: current == 4 }" @click="$replacePage('/information/index')">
				<div class="icon a4 animate__animated animate__fadeIn" :class="{ ac4: current == 4 }"></div>
				<div class="t">我的</div>
			</div> -->
		</div>
	</div>
</template>

<script>
	export default {
		name: "tab-bar",
		props: {
			current: {
				type: Number,
				default: "",
			},
		},
		data() {
			return {};
		},
		components: {},
		methods: {},
		created() {},
		computed: {},
	};
</script>

<style scoped lang="less">
	.tab {
		height: 0.7rem;
		background: #fff;
		width: 100%;
		position: fixed;
		padding-top: .1rem;
		bottom: 0;
		left: 0;
		z-index: 999;
		border-top: 0.01rem solid #ededed;
		.item {
			padding: 0.05rem 0;
			text-align: center;
			flex: 1;

			&.active {
				.t {
					color: #DB5D4F;
				}
			}

			.icon {
				margin: 0 auto;
			}

			.t {
				margin-top: 0.02rem;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				color: #405476;
			}
		}
	}
</style>