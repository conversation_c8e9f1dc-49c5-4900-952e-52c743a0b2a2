<template>
	<div class="page ">
		<top-back title="提領" :isList="true"></top-back>

		<div class="cot">
			<div class="money">
				<div class="t flex">
					可用資金
					<div class="icon bageye animate__animated animate__fadeIn" @click="show = !show"></div>
				</div>
				<div class="t1">
					NT$ {{ show ? $formatMoney(userInfo.twd,0) : "****" }}
				</div>
			</div>

			<div class="bg">
				<!-- <div class="nums">{{ $formatMoney(money) }}</div>
				<div class="title">提領金額</div> -->
				<div class="tt">金額</div>
				<div class="ipt">
					<input class="input" v-model="money" type="number" placeholder="請輸入提領金額" />
				</div>
				<div class="money-list">
					<div class="inner flex flex-b">
						<div class="money-item flex flex-c" v-for="(item, index) in moneyList" :key="index"
							:class="{ active: currmentIndex == index }" @click="changeMoney(index)">
							{{ $formatMoney(item.money,0) }}
						</div>
					</div>
				</div>
				<!-- 选择银行卡 -->
				<div class="list">
					<div class="list-title flex flex-b" @click="$toPage('/information/addBankCard')">
						提領至
						<div class="icon jtr" v-if="!bankList.length"></div>
					</div>
					<div class="list-item" v-for="(item, index) in bankList" :key="index" @click="itemObj(item)">
						<div class="item-name">
							{{ item.bank_name }}
							<span>(金融卡{{item.bank_num.substring(item.bank_num.length - 4)}})</span>
						</div>
						<div class="icon yxz animate__animated animate__fadeIn" v-if="bankId && bankId == item.id">
						</div>
						<div class="icon wxz animate__animated animate__fadeIn" v-else></div>
					</div>
				</div>
				<div class="tt">資金密碼</div>
				<div class="ipt">
					<input class="input" v-model="moneyPwd" type="password" placeholder="請輸入資金密碼" />
				</div>
				<!-- <div class="tips">
					<div class="t">{{ $t("new").b32 }}</div>
					<div class="t1">{{ $t("new").b33 }}</div>
					<div class="t1">{{ $t("new").b34 }} {{ open }}-{{ close }}</div>
					<div class="t1">{{ $t("new").b35 }} $ {{ minwithdrawal }}</div>
					<div class="t1">{{ $t("new").b36 }}</div>
					<div class="t1">{{ $t("new").b37 }}</div>
				</div> -->
				<div class="" style="margin-top:0.2rem;">
					<div class="b-btn animate__animated animate__fadeIn" @click="submit">
						確認
					</div>
				</div>
			</div>
		</div>

		<loading ref="loading" />
	</div>
</template>

<script>
	export default {
		name: "cashOut",
		props: {},
		data() {
			return {
				currmentIndex: -1,
				show: true,
				bankList: [],
				userInfo: {},
				isChg: "",
				money: "",
				moneyList: [{
						money: "5000"
					},
					{
						money: "10000"
					},
					{
						money: "15000"
					},
					{
						money: "20000"
					},
					{
						money: "25000"
					},
					{
						money: "30000"
					},
				],
				moneyPwd: "",
				bankId: "",
				minwithdrawal: "",
				open: "",
				close: "",
			};
		},
		components: {},
		created() {
			this.getUserInfo();
			this.initData();
			this.getConfig();
		},
		computed: {},
		methods: {
			changeMoney(index) {
				this.currmentIndex = index;
				this.money = this.moneyList[index].money.replace(/,/g, "");
			},
			itemObj(item) {
				this.bankId = item.id;
			},
			submit() {
				let that = this;
				// 沒有設置密碼前往設置
				if (!this.isChg) {
					this.$toast("請設置資金密碼");
					setTimeout(() => {
						this.$toPage("/information/fundPass");
					}, 2000);
					return;
				}

				// 没有银行卡跳转添加
				if (this.bankList.length == 0) {
					this.$toast("請添加金融卡");
					setTimeout(() => {
						this.$toPage("/information/addBankCard");
					}, 2000);
					return;
				}

				// 如果沒有銀行卡，前往添加
				if (!this.bankId) {
					this.$toast("請選擇金融卡");
					return;
				}

				if (!this.money) {
					this.$toast("請輸入提領金額");
					return;
				}

				if (!this.moneyPwd) {
					this.$toast("請輸入資金密碼");
					return;
				}

				if (this.moneyPwd == this.userInfo.passwords) {
					this.$refs.loading.open(); //开启加载
					this.$server.post("/user/withdrawal", {
						money: this.money,
						bankid: this.bankId,
						passwords: this.moneyPwd,
						type: "twd",
					}).then((res) => {
						this.$refs.loading.close();
						if (res.status == 1) {
							this.$toast(this.$t(res.msg));
							setTimeout(() => {
								this.$router.go(-1);
							}, 2000);
						}
					});
				} else {
					// 输入的资金密码和设置的不一致
					this.$toast("資金密碼錯誤");
				}
			},
			getConfig() {
				this.$server.post("/common/config", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						let list = res.data;
						let listLength = list.length;
						let a;
						for (a = 0; a < listLength; a++) {
							if (list[a].name === "swithdrawal") {
								this.open = list[a].value;
							}
							if (list[a].name === "ewithdrawal") {
								this.close = list[a].value;
							}
							if (list[a].name === "minwithdrawal") {
								this.minwithdrawal = list[a].value;
							}
							// if (list[a].name === 'kefu') {
							//   this.customer = list[a].value;
							// }
						}
					}
				});
			},
			getUserInfo() {
				this.$server.post("/user/getUserinfo", {
					type: "twd"
				}).then((res) => {
					if (res.status == 1) {
						this.userInfo = res.data;
            let mm = this.userInfo.twd.toString().split('.')
            this.userInfo.twd = mm[0]
						this.isChg = !!res.data.passwords;
					}
				});
			},
			initData() {
				this.$server.post("/user/cardList", {
					size: 200,
					page: 1,
					type: "twd",
				},(failres) => {
					that.bankList = [];
				}).then((res) => {
						var arr = [];
						for (var i in res.data) {
							var row = res.data[i];
							if (row.bank_name != "TRC" && row.bank_name != "ERC") {
								arr.push(row);
							}
						}
						this.bankList = arr;
					});
			},
		},
	};
</script>

<style scoped lang="less">
	.page {
		padding: 0.5rem 0rem 0;
		min-height: 100vh;
	}

	.tips {
		padding: 0.15rem 0;

		.t {
			color: #636363;
		}

		.t1 {
			font-size: 0.12rem;
			color: #636363;
			line-height: 0.2rem;
		}
	}

	.cot {
		.money {
			padding: 0.2rem 0.15rem 0.5rem;
			background: url('../../assets/v2/bg02.png') no-repeat center/100%;

			.t {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 0.13rem;
				color: #FFFFFF;

				.icon {
					margin-left: 0.05rem;
				}
			}

			.t1 {
				margin-top: 0.1rem;
				font-weight: 600;
				font-size: 0.26rem;
				color: #ffffff;
				font-family: Poppins, Poppins;
			}
		}

		.bg {
			background: #FFFFFF;
			border-radius: 0.15rem 0.15rem 0rem 0rem;
			padding: 0.2rem 0.15rem;
			margin: -0.3rem 0 0;

			.nums {
				font-family: Poppins, Poppins;
				font-weight: bold;
				font-size: 0.3rem;
				color: #39324b;
				text-align: center;
			}

			.title {
				color: #6b6b6b;
				text-align: center;
			}
		}

		.money-list {
			padding: 0.2rem 0 0.2rem;

			.title {
				font-weight: 600;
				color: #0e1028;
				margin-bottom: 0.1rem;
			}

			.inner {
				flex-wrap: wrap;

				.money-item {
					width: 32%;
					height: 0.58rem;
					background: #F8F9FD;
					border-radius: 0.04rem;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 0.17rem;
					color: #182133;
					padding: 0 0.1rem;
					text-align: center;
					margin-bottom: 0.1rem;

					&.active {
						background: #F9F0E7;
						border-radius: 0.04rem;
						border: 0.01rem solid #DCA25C;
					}
				}
			}
		}

		.tt {
			font-weight: 600;
			color: #0e1028;
			margin: 0 0 0.15rem;
		}

		.ipt {
			border-bottom: 0.01rem solid #cecece;

			.input {
				width: 100%;
				background: transparent;
				height: 0.42rem;
				color: #000;

				&::placeholder {
					font-size: 0.14rem;
					color: #aaaaaa;
				}
			}
		}

		.list {
			// padding: 0 0.15rem;

			.list-title {
				font-weight: 600;
				margin: 0 0 0.15rem;
			}

			.list-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 0.15rem;
				// .checked {
				//   width: 0.2rem;
				//   height: 0.2rem;
				//   border-radius: 50%;
				//   background: #6970af;
				//   border: 0.02rem solid #f4f4f4;
				// }
				// .nocheck {
				//   width: 0.2rem;
				//   height: 0.2rem;
				//   border-radius: 50%;
				//   border: 0.02rem solid #dedede;
				// }

				.item-name {
					display: flex;
					align-items: center;
					font-size: 0.12rem;

					span {
						font-size: 0.12rem;
						padding-left: 0.05rem;
					}
				}
			}
		}

		.b-btn {
			margin: 0;
		}
	}
</style>