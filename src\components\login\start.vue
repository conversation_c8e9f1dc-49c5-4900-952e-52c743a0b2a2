<template>
	<div class="page flex flex-b">
		<div class="flex flex-c">
			
			<!-- <div class="txt" style="text-align: center;">
				每一天都是新的開始<br/>
				帶著微笑迎接無限可能<br/>
				長悅資本投資，成就未來每一刻
			</div> -->
		</div>
		<!-- <div class="text-container">
			<div class="line">啟動中...</div>
			<div class="line">加載首頁菜單...</div>
			<div class="line">獲取用戶登入狀態...</div>
			<div class="line">獲取用戶信息⋯</div>
			<div class="line">獲取配置文件...</div>
			<div class="line">加載底部tab..</div>
			<div class="line">加載完成...</div>
		</div> -->
		<div class="load flex flex-c">
			<img  src="../../assets/v2/loading.png" style="width: 0.31rem;height: 0.31rem;" alt=""  />
		</div>
		<!-- <img class="img" src="../../assets/v2/logobg.png" style="width: 100%;height: 3.74rem;" alt="" /> -->
	</div>
</template>

<script>
	export default {
		name: "start",
		props: {},
		data() {
			return {};
		},
		components: {},
		mounted() {
			this.setInt();
		},
		methods: {
			setInt() {
				setTimeout(() => {
					this.startTime();
				}, 2000);
			},

			startTime() {
				if (localStorage.getItem("tokend") && localStorage.getItem("account")) {
					this.$replacePage("/home/<USER>");
				} else {
					this.$replacePage("/login/login");
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.text-container {
		display: flex;
		justify-content: end;
		flex-direction: column;
		width: 100%;
		position: absolute;
		left: 0.2rem;
		top: 50%;
		transform: translateY(-50%);
		z-index: 999;
		.line {
			margin: 0;
			opacity: 0;
			font-size: 0.16rem;
			color: #000;
			font-weight: bold;
			margin-bottom: 0.15rem;
			animation: showLine 1s ease forwards;
		}
	}

	@keyframes showLine {
		0% {
			opacity: 0;
			transform: translateY(0.05rem);
		}

		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}
	@keyframes show {
		0% {
			opacity: 0;
		}
	
		100% {
			opacity: 1;
		}
	}
	@keyframes roate {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	/* 设置每行的延迟，以便逐行显示 */
	.line:nth-child(1) {
		animation-delay: 0.5s;
	}

	.line:nth-child(2) {
		animation-delay: 1s;
	}

	.line:nth-child(3) {
		animation-delay: 1.5s;
	}

	.line:nth-child(4) {
		animation-delay: 2s;
	}

	.line:nth-child(5) {
		animation-delay: 2.5s;
	}

	.line:nth-child(6) {
		animation-delay: 3s;
	}

	.line:nth-child(7) {
		animation-delay: 3.5s;
	}

	.page {
		width: 100vw;
		min-height: 100vh;
		// background: #fff;
		background: url('../../assets/start124.png?1');
		background-size: 100%;
		
		padding: 1rem 0 0;
		flex-direction: column;
		.img{
			position: absolute;
			bottom: 0;
			left: 0;
		}
		.txt {
			animation: showLine 1s ease forwards;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 0.16rem;
			color: #374C6A;
			margin: 0.2rem 0;
			position: absolute;
			bottom: 10%;
		}
		.load{
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%,-50%);
			z-index: 999;
			width: 0.89rem;
			height: 0.89rem;
			background: rgba(0, 0, 0, 0.5);
			border-radius: 0.11rem;
			animation: show 2s ease forwards;
			img{
				animation: roate 1.5s infinite linear;
			}
		}
		.txt1 {
			font-size: 0.16rem;
			color: #59a082;
		}
	}
</style>